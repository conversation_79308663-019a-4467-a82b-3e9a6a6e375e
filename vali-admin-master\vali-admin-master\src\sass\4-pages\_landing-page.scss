.landing-hero {
	position: relative;
	background-size: cover;
	background-repeat: no-repeat;
	background-image: url(https://images.unsplash.com/photo-1487621167305-5d248087c724?ixlib=rb-1.2.1&q=80&fm=jpg&crop=entropy&cs=tinysrgb&w=1600&h=700&fit=crop&ixid=eyJhcHBfaWQiOjF9);
	border-bottom: 1px solid #ddd;
}

.landing-hero-overlay {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: darken($primary-color, 15);
	opacity: 0.5;
	z-index: 1;
}

.landing-hero-content {
	display: flex;
	flex-direction: column;
	justify-content: flex-end;;
	position: relative;
	padding: 60px 30px 0;
	z-index: 2;
	text-align: center;
	color: #fff;
	@media(min-width: 768px) { min-height: 100vh; }
}

.landing-hero-title {
	font-size: 26px;
	@media(max-width: 480px) { font-size: 20px; }
}

.landing-hero-description { font-size: 15px; }

.landing-hero-banner {
	display: block;
	width: 100%;
	max-width: 900px;
	margin: 30px auto 0;
	border-radius: 6px 6px 0 0;
}

.features {
	padding: 60px 30px;
}

.features-title {
	font-size: 26px;
	text-align: center;
	margin-bottom: 30px;
}

.features-cards {
	max-width: 760px;
	list-style: none;
	margin: 0 auto;
	padding-left: 0;
	display: grid;
	grid-gap: 20px;
	grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
}

.features-card {
	border-radius: 4px;
	padding: 10px;
	font-size: 1.143em;
	text-align: center;
}

.feature-icon-backdrop {
	width: 64px;
	height: 64px;
	margin: 0 auto 8px;
	padding: 15px;
	border-radius: 50%;
	background-color: $primary-color;
}

.feature-icon {
	width: 32px;
	height: 32px;
	stroke: #fff;
	fill: transparent;
}

.landing-footer {
	padding: 20px;
	text-align: center;
	border-top: 1px solid #ddd;
}
