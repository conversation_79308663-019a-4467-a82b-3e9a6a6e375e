header.app-header
	a.app-header__logo(href='index.html') Vali

	// Sidebar toggle button
	a.app-sidebar__toggle(href='#', data-toggle='sidebar', aria-label="Hide Sidebar")

	// Navbar Right Menu
	ul.app-nav

		li.app-search
			input.app-search__input(type="search", placeholder="Search")
			button.app-search__button: i.bi.bi-search

		//Notification Menu
		li.dropdown
			a.app-nav__item(href='#', data-bs-toggle='dropdown' aria-label="Show notifications")
				i.bi.bi-bell.fs-5
			ul.app-notification.dropdown-menu.dropdown-menu-right
				li.app-notification__title You have 4 new notifications.
				.app-notification__content
					li
						a.app-notification__item(href='javascript:;')
							span.app-notification__icon
								i.bi.bi-envelope.fs-4.text-primary
							div
								p.app-notification__message Lisa sent you a mail
								p.app-notification__meta 2 min ago

					li
						a.app-notification__item(href='javascript:;')
							span.app-notification__icon
								i.bi.bi-exclamation-triangle.fs-4.text-warning
							div
								p.app-notification__message Mail server not working
								p.app-notification__meta 5 min ago

					li
						a.app-notification__item(href='javascript:;')
							span.app-notification__icon
								i.bi.bi-cash.fs-4.text-success
							div
								p.app-notification__message Transaction complete
								p.app-notification__meta 2 days ago

					li
						a.app-notification__item(href='javascript:;')
							span.app-notification__icon
								i.bi.bi-envelope.fs-4.text-primary
							div
								p.app-notification__message Lisa sent you a mail
								p.app-notification__meta 2 min ago

					li
						a.app-notification__item(href='javascript:;')
							span.app-notification__icon
								i.bi.bi-exclamation-triangle.fs-4.text-warning
							div
								p.app-notification__message Mail server not working
								p.app-notification__meta 5 min ago

					li
						a.app-notification__item(href='javascript:;')
							span.app-notification__icon
								i.bi.bi-cash.fs-4.text-success
							div
								p.app-notification__message Transaction complete
								p.app-notification__meta 2 days ago

				li.app-notification__footer
					a(href='#') See all notifications.

		// User Menu
		li.dropdown
			a.app-nav__item(href='#', data-bs-toggle='dropdown' aria-label="Open Profile Menu")
				i.bi.bi-person.fs-4
			ul.dropdown-menu.settings-menu.dropdown-menu-right
				li
					a.dropdown-item(href='page-user.html')
						i.bi.bi-gear.me-2.fs-5
						|  Settings
				li
					a.dropdown-item(href='page-user.html')
						i.bi.bi-person.me-2.fs-5
						|  Profile
				li
					a.dropdown-item(href='page-login.html')
						i.bi.bi-box-arrow-right.me-2.fs-5
						|  Logout
