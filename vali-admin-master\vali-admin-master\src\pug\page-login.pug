doctype html
html
	head
		include includes/_head.pug
		title Login - <PERSON>i Admin

	body
		section.material-half-bg
			.cover

		section.login-content
			.logo: h1 Vali

			.login-box
				form.login-form(action="index.html")
					h3.login-head
						i.bi.bi-person.me-2
						| SIGN IN
					.mb-3
						label.form-label USERNAME
						input.form-control(type="text", placeholder="Email", autofocus)

					.mb-3
						label.form-label PASSWORD
						input.form-control(type="password", placeholder="Password")

					.mb-3
						.utility
							.form-check
								label.form-check-label
									input.form-check-input(type="checkbox")
									span.label-text Stay Signed in
							p.semibold-text.mb-2
								a(href="#" data-toggle="flip") Forgot Password ?

					.mb-3.btn-container.d-grid
						button.btn.btn-primary.btn-block
							i.bi.bi-box-arrow-in-right.me-2.fs-5
							| SIGN IN

				form.forget-form(action="index.html")
					h3.login-head
						i.bi.bi-person-lock.me-2
						| Forgot Password ?
					.mb-3
						label.form-label EMAIL
						input.form-control(type="text", placeholder="Email")

					.mb-3.btn-container.d-grid
						button.btn.btn-primary.btn-block
							i.bi.bi-unlock.me-2.fs-5
							| RESET

					.mb-3.mt-3
						p.semibold-text.mb-0
							a(href="#" data-toggle="flip") <i class="bi bi-chevron-left me-1"></i> Back to Login

		include includes/_javascript.pug

		script(type="text/javascript").
			// Login Page Flipbox control
			$('.login-content [data-toggle="flip"]').click(function() {
				$('.login-box').toggleClass('flipped');
				return false;
			});