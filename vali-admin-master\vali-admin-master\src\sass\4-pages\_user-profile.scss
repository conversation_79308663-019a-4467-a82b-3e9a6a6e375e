// Page: User Profile

.user {
	.profile {
		margin: -30px -30px 0 -30px;
		position: relative;

		.info {
			padding: 30px 60px;
			text-align: center;
			background-color: #fff;
			white-space: nowrap;
			background-color: rgba(100, 100, 100, 0.9);
			color: #fff;
			position: absolute;
			top: 0;
			bottom: 0;
			left: 0;

			img {
				border-radius: 50%;
				max-width: 110px;
				margin-bottom: 20px;
				margin-top: 10px;
			}
		}

		.cover-image {
			flex: 1;
			background-image: url(https://unsplash.com/photos/dH4JzW5h9Ng/download?w=1920);
			background-size: cover;
			background-position: center;
			min-height: 300px;
		}
	}

	.user-tabs {
		margin-top: 20px;

		.nav-link {
			border-left: 3px solid transparent;
			padding: 12px 15px;
			border-bottom: 0;
			font-size: 15px;
			border-radius: 0;

			&.active {
				border-left-color: $primary-color;
				border-bottom: 0;
			}

			&:hover,
			&:active {
				background-color: #eee !important;
				border-bottom: 0;
			}
		}
	}

	.tab-content { margin: 20px -10px -10px; }

	.timeline-post {
		background-color: #fff;
		padding: 20px;
		margin-bottom: 20px;
		box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 3px 1px -2px rgba(0, 0, 0, 0.2), 0 1px 5px 0 rgba(0, 0, 0, 0.12);

		.post-media {
			display: flex;
			align-items: flex-start;
			margin-bottom: 15px;

			img {
				margin-right: 10px;
				max-width: 45px;
				height: auto;
				border-radius: 4px;
			}

			h5 {
				margin-top: 0;
				margin-bottom: 3px;
				font-size: 15px;
			}
		}

		.post-content { margin-bottom: 20px; }

		.post-utility {
			display: flex;
			list-style: none;
			padding-left: 0;
			margin-bottom: 0;

			.likes,
			.shares {
				margin-right: 10px;

				a { color: #555; }
			}

			.comments {
				color: #555;
				flex: 1;
				text-align: right;
			}
		}
	}

	.user-settings {
		.line-head {
			font-size: 18px;
			margin-bottom: 30px;
			color: #2a2a2a;
		}

		label { color: #666; }
	}
}