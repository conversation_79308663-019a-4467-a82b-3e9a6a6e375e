// Componant: Material Loader

.m-loader {
	position: relative;
	width: 40px;
	&:before {
		content: '';
		display: block;
		padding-top: 100%;
	}
}

.m-circular {
	animation: rotate 1.5s linear infinite;
	height: 100%;
	transform-origin: center center;
	width: 100%;
	position: absolute;
	top: 0;
	bottom: 0;
	left: 0;
	right: 0;
	margin: auto;
}
.path {
	stroke-dasharray: 1,200;
	stroke-dashoffset: 0;
	stroke: $primary-color;
	animation: dash 1.5s ease-in-out infinite;
	stroke-linecap: round;
}
@keyframes rotate {
	100% { transform: rotate(360deg); }
}
@keyframes dash {
	0% {
		stroke-dasharray: 1,200;
		stroke-dashoffset: 0;
	}
	50% {
		stroke-dasharray: 89,200;
		stroke-dashoffset: -35px;
	}
	100% {
		stroke-dasharray: 89,200;
		stroke-dashoffset: -124px;
	}
}


// Usage Example
// <div class="m-loader mr-20">
// 	<svg class="m-circular" viewBox="25 25 50 50">
// 		<circle class="path" cx="50" cy="50" r="20" fill="none" stroke-width="4" stroke-miterlimit="10"></circle>
// 	</svg>
// </div>