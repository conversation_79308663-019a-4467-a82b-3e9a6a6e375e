// Componant: Sidebar 

.app-sidebar {
	position: fixed;
	top: 0;
	bottom: 0;
	left: 0;
	padding-top: 70px;
	width: $sidebar-width;
	overflow: auto;
	z-index: 10;
	background-color: $sidebar-color;
	box-shadow: 0px 8px 17px rgba(0, 0, 0, 0.2);
	transition: left 0.3s ease,
				width 0.3s ease;
	&::-webkit-scrollbar { width: 6px; }
	&::-webkit-scrollbar-thumb { background: rgba(0,0,0,0.2); }
	@media print { display: none; }
}

.app-sidebar__overlay {
	@media(max-width: 767px) {
		position: fixed;
		top: 0;
		left: 0;
		bottom: 0;
		right: 0;
		z-index: 9;
	}
}

.app-sidebar__user {
	display: flex;
	align-items: center;
	padding: 10px;
	margin-bottom: 20px;
	@if $sidebar-accent == dark {
		color: #fff;
	}
}

.app-sidebar__user-avatar {
	@extend .rounded-circle;
	flex: 0 0 auto;
	margin-right: 15px;
	max-width: 45px;
	height: auto;
}

.app-sidebar__user-name {
	font-size: 17px;
	line-height: 1.3;
}

.app-sidebar__user-name,
.app-sidebar__user-designation {
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	margin-bottom: 0;
}

.app-menu {
	@extend .list-unstyled;
	margin-bottom: 0;
	padding-bottom: 40px;
}

.app-menu__item {
	position: relative;
	display: flex;
	align-items: center;
	padding: 12px 15px;
	font-size: 1.08em;
	border-left: 3px solid transparent;
	transition: border-left-color 0.3s ease,
				background-color 0.3s ease;
	text-decoration: none;
	@if $sidebar-accent == dark { color: $sidebar-dark-link-color }
	@if $sidebar-accent == light { color: $sidebar-light-link-color }
	&.active,
	&:hover,
	&:focus {
		background: darken($sidebar-color, 10);
		border-left-color: $primary-color;
		text-decoration: none;
		@if $sidebar-accent == dark { color: $sidebar-dark-link-color }
		@if $sidebar-accent == light { color: $primary-color }
	}
}

.app-menu__icon {
	flex: 0 0 auto;
	width: 25px;
}

.app-menu__label {
	white-space: nowrap;
	flex: 1 1 auto;
}

.treeview {
	&.is-expanded {
		[data-toggle='treeview'] {
			border-left-color: $primary-color;
			background: darken($sidebar-color, 10);
		}
		.treeview-menu { max-height: 100vh; }
		.treeview-indicator { transform: rotate(90deg); }
	}
}

.treeview-menu {
	@extend .list-unstyled;
	max-height: 0;
	overflow: hidden;
	transition: max-height 0.3s ease;
	@if $sidebar-accent == dark {
		background: lighten($sidebar-color, 4)
	}
	@if $sidebar-accent == light {
		background: darken($sidebar-color, 4)
	}
}

.treeview-item {
	display: flex;
	align-items: center;
	padding: 5px 5px 5px 20px;
	font-size: 1em;
	text-decoration: none;
	@if $sidebar-accent == dark { color: $sidebar-dark-link-color }
	@if $sidebar-accent == light { color: $sidebar-light-link-color }
	&.active,
	&:hover,
	&:focus {
		background: darken($sidebar-color, 10);
		text-decoration: none;
		@if $sidebar-accent == dark { color: $sidebar-dark-link-color }
		@if $sidebar-accent == light { color: $primary-color }
	}
	.icon { margin-right: 5px; }
}

.treeview-indicator {
	transform-origin: center;
	transition: transform 0.3s ease;
}
