@extends('theme.layout.master')

@section('content')
<div class="container">
    <h2>All Categories</h2>

    <a href="{{ route('categories.create') }}" class="btn btn-primary mb-3">Add New Category</a>

    <table class="table table-bordered">
        <thead>
            <tr>
                <th>ID</th>
                <th>Name</th>
                <th>Slug</th>
                <th>Status</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            @foreach($categories as $category)
            <tr>
                <td>{{ $category->id }}</td>
                <td>{{ $category->name }}</td>
                <td>{{ $category->slug }}</td>
                <td>{{ $category->status ? 'Active' : 'Inactive' }}</td>
                <td><a href="{{ route('categories.edit', $category->id) }}" class="btn btn-sm btn-warning">Edit</a></td>
            </tr>
            @endforeach
        </tbody>
    </table>
</div>
@if(session('success'))
    <div class="alert alert-success">
        {{ session('success') }}
    </div>

@else(session('error'))
    <div class="alert alert-danger">
        {{ session('error') }}
    </div>
@endif

@endsection
