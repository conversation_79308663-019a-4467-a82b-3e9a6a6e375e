// Componant: Page Title

.app-title {
	display: flex;
	align-items: center;
	justify-content: space-between;
	flex-direction: row;
	background-color: #FFF;
	margin: (-30px) -30px 30px;
	padding: 20px 30px;
	box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);

	@media print { display: none; }
	@media (max-width: 480px) {
		margin: (-15px) -15px 15px;
		padding: 20px;
		flex-direction: column;
		align-items: flex-start;
	}

	h1 {
		margin: 0;
		font-size: 24px;
		font-weight: 400;
	}

	p {
		margin-bottom: 0;
		font-style: italic;
		@media (max-width: 480px) { display: none; }
	}
}

.app-breadcrumb {
	margin-bottom: 0;
	text-align: right;
	font-weight: 500;
	font-size: 13px;
	text-transform: capitalize;
	padding: 0;
	text-align: left;
	padding: 0;
	background-color: transparent;
	@media (max-width: 480px) { margin-top: 10px; }
}