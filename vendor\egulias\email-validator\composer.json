{"name": "egulias/email-validator", "description": "A library for validating emails against several RFCs", "homepage": "https://github.com/egulias/EmailValidator", "keywords": ["email", "validation", "validator", "emailvalidation", "emailvalidator"], "license": "MIT", "authors": [{"name": "<PERSON>"}], "extra": {"branch-alias": {"dev-master": "3.0.x-dev"}}, "require": {"php": ">=7.2", "doctrine/lexer": "^1.2|^2", "symfony/polyfill-intl-idn": "^1.15"}, "require-dev": {"phpunit/phpunit": "^8.5.8|^9.3.3", "vimeo/psalm": "^4"}, "suggest": {"ext-intl": "PHP Internationalization Libraries are required to use the SpoofChecking validation"}, "autoload": {"psr-4": {"Egulias\\EmailValidator\\": "src"}}, "autoload-dev": {"psr-4": {"Egulias\\EmailValidator\\Tests\\": "tests"}}}