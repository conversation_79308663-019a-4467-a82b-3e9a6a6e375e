extends layouts/_layout.pug

block variables
	- var activePage = 'docs';
	- var activeGroup = 'none';

block title
	title Documentation - Vali Admin

block content
	.app-title
		div
			h1
				i.bi.bi-code-square
				|  Documentation
			p Documentation of vali admin

		ul.app-breadcrumb.breadcrumb
			li.breadcrumb-item
				i.bi.bi-house-door.fs-6
			li.breadcrumb-item
				a(href='#') Documentation


	.tile
		.tile-body
			.docs(style="max-width: 700px;")
				h2.docs-title Directory Structure

				pre.directory-structure.
					│
					├── docs - <i>compiled files</i>
					│   ├── css
					│   ├── images
					│   └── js
					└── src - <i>Layout and style source files</i>
					&nbsp;&nbsp;&nbsp;&nbsp;├── pug - <i>Layout source</i>
					&nbsp;&nbsp;&nbsp;&nbsp;└── sass - <i>Style source</i>

				h2.docs-title#Compilation-of-source-files Compilation of source files

				p The theme is built using SASS and PugJs which are in turn compiled into HTML and CSS by Grunt. If you are not familiar with <PERSON><PERSON><PERSON>, #[a(href="https://24ways.org/2013/grunt-is-not-weird-and-hard/", target="_blank", rel="noopener") here] is an article to get started. If you are familiar with Grunt follow the instruction mentioned bellow to edit or customize the source. 

				p If you don't want to edit theme you can use the compiled files directly inside #[code docs] folder.

				p Run #[code npm install] command in project root directory to install and build dependencies.

				p Use #[code npm run dev] task to edit and compile source files on the go or use #[code npm run build] task to compile all source files at once.

				h2.docs-title#Layout-Customization Layout Customization

				p The layout is built using PugJs. All the layout source files are located in #[code src/pug] directory. There are two sub directories inside this directory: 

					ol
						li #[code layout] - Includes common HTML skeleton layout which is extended by all the pages
						li #[code includes] - Includes layout partials like sidebar and navbar and footer

				h2.docs-title#Style-Customization Style Customization
				p The styles are written in SASS. All the style files are located in #[code src/sass] directory. There is a file in this directory #[code main.sass] which imports all the files and exported as main.css There are four sub directories inside this directory:

					ol
						li #[code 1-tools] - It includes styles of all the external libraries and a file #[code _var.scss] which contains the variables required for the application
						li #[code 2-basics] - It contains the basic style like overall structure css and theming options
						li #[code 3-component] - It contains the styles for the components like card, widgets, sidebar, navbar etc
						li #[code 4-pages] - It contains the styles for the specific pages like login page, lock-screen page 
				
				p To customize the primary color of the theme and sidebar you need to change the variables in the #[code 1-tools/_var.scss]. The detailed documentation about changing the colors is mentioned in this file itself.

				p If you don't want to use particular component or plug-in just comment the import statement for that particular component in #[code src/sass/main.scss] and compile the SASS by running #[code npm run build] command.

				h2.docs-title#Compatibility-with-other-frameworks Compatibility with other frameworks
				p This theme is not built for a specific framework or technology like Angular or React etc. But due to it's modular nature it's very easy to incorporate it into any front-end or back-end framework like Angular, React or VueJs or Node JS. The CSS is modular enough to be incorporated in any framework. While the Javascript used to make the components interactive can be used from any of the following framework.

				p If you are using Angular you can use #[a(href="https://angular-ui.github.io/bootstrap/", rel="noopener") ui-bootstrap], for React use #[a(href="https://react-bootstrap.github.io/", rel="noopener") React-Bootstrap] and for VueJs you can use #[a(href="https://yuche.github.io/vue-strap/", rel="noopener") VueStrap].

				p If you are using Node JS as your web server you can use pug as your layout engine to render html templates as is without compiling them to HTML. More details are available #[a(href="https://pugjs.org/api/express.html", target="_balnk") here].

				h2.docs-title#RTL-Support RTL Support

				p To enable RTL support
					ul
						li Uncomment this line #[code @import '3-component/rtl';] in #[code src/sass/main.scss]. 
						li Add #[code dir="rtl"] attribute to #[code &lt;html&gt;] tag in #[code src/pug/layouts/_layout.pug].
						li Build the source files using #[code npm run build] command.

				h2.docs-title#Contribution-and-Issues Contribution & Issues

				p If you liked the theme do star and fork it on #[a(href="https://github.com/pratikborsadiya/vali-admin", target="_blank", rel="noopener") GitHub]. If you find anything missing or want to contribute to this documentation, the source is available #[a(href="https://github.com/pratikborsadiya/vali-admin/blob/master/src/pug/docs.pug", target="_blank", rel="noopener") here]. If you have an issue or feature request regarding theme please report it #[a(href="https://github.com/pratikborsadiya/vali-admin/issues/new", target="_blank", rel="noopener") here].