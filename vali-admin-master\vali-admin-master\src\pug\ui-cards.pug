extends layouts/_layout.pug

block variables
	- var activePage = 'ui-cards';
	- var activeGroup = 'ui-elements';

block title
	title Cards - Vali Admin

block content
	.app-title
		div
			h1
				i.bi.bi-laptop
				|  Cards
			p Material design inspired cards

		ul.app-breadcrumb.breadcrumb
			li.breadcrumb-item
				i.bi.bi-house-door.fs-6
			li.breadcrumb-item UI
			li.breadcrumb-item
				a(href="#") Cards

	.row
		.col-md-6
			.tile
				h3.tile-title Card Title
				.tile-body.
					Hey there, I am a very simple card. I am good at containing small bits of information. I am quite convenient because I require little markup to use effectively.
				.tile-footer
					a.btn.btn-primary(href="#") Link

		.col-md-6
			.tile
				.tile-title-w-btn
					h3.title All Items
					p
						a.btn.btn-primary.icon-btn(href="")
							i.bi.bi-plus-square.me-2
							| Add Item	
				.tile-body.
					<b>Card with action button </b><br>
					Hey there, I am a very simple card. I am good at containing small bits of information. I am quite convenient because I require little markup to use effectively.


		.clearfix

		.col-md-6
			.tile
				.tile-title-w-btn
					h3.title All Items
					.btn-group
						a.btn.btn-primary(href='#'): i.bi.bi-plus-square.fs-5
						a.btn.btn-primary(href='#'): i.bi.bi-pencil-square.fs-5
						a.btn.btn-primary(href='#'): i.bi.bi-trash.fs-5
				.tile-body.
					<b>Card with button group </b><br>
					Hey there, I am a very simple card. I am good at containing small bits of information. I am quite convenient because I require little markup to use effectively.

		.col-md-6
			.tile
				.overlay
					.m-loader.mr-4.
						<svg class="m-circular" viewBox="25 25 50 50">
							<circle class="path" cx="50" cy="50" r="20" fill="none" stroke-width="4" stroke-miterlimit="10"/>
						</svg>
					h3.l-text Loading
				.tile-title-w-btn
					h3.title All Items
					p
						a.btn.btn-primary.icon-btn(href="")
							i.bi.bi-plus-square
							| Add Item	
				.tile-body.
					<b>Card with action button </b><br>
					Hey there, I am a very simple card. I am good at containing small bits of information. I am quite convenient because I require little markup to use effectively.
