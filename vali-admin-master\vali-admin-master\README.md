# Vali Admin

![vali-admin](http://pratikborsadiya.in/blog/vali-admin/vali-admin-banner.gif)

[![npm version](https://badge.fury.io/js/vali-admin.svg)](https://badge.fury.io/js/vali-admin)

Vali is a free, modular and easy to customize admin theme built using [Bootstrap 5](https://getbootstrap.com), [SASS](http://sass-lang.com) and [Pug.js](https://pugjs.org).

## Getting started

Run a `npm install` command in project root directory to install and build dependencies. If you don't want to edit theme you can use the compiled files inside `docs` folder.

Use `npm run dev` command to watch and compile source files.

Use `npm run build` command to compile all source files.

Use `npm run start` command to start a development server using NodeJs.

> **Note:**
> * The NodeJs server mentioned in `npm run start` command is for development purpose only. DONOT use it as a production server.

## Customization
For more information about customizing theme colors please follow the official [documentation](https://pratikborsadiya.in/vali-admin/docs.html).

## RTL Support
Please follow the official [documentation](https://pratikborsadiya.in/vali-admin/docs.html) to enable RTL support.

## Contributing
Please take a look at [contributing guidelines](CONTRIBUTING.md) if you are considering contributing to the repository.

## Contributors

* **[Pratik Borsadiya](http://pratikborsadiya.in)** - *Project Author*
* **List of [contributors](https://github.com/pratikborsadiya/vali-admin/graphs/contributors)** who participated in this project.

## License

This project is licensed under the [MIT](LICENSE) License
