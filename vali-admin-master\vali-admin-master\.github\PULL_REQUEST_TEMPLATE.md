## Pull Request Guidelines
Please, go through these steps before you submit a pull request.

1. Make sure that your pull request is not a duplicate.
2. If not, then make sure that:

	2.1. You have done your changes in a separate branch. Branches MUST have descriptive names that start with either the `fix/` or `feature/` prefixes. Good examples are: `fix/signin-issue` or `feature/issue-templates`.

	2.2. You have a descriptive commit message with a short title (first line).

	2.2. You have created one pull request for one purpose only. Create separate branches and separate pull requests for each newly introduced feature or for each fixed issue.

	2.3. You have only one commit (if not, squash them into one commit).

3. **After** these steps, you're ready to open a pull request.

	3.1. Your pull request MUST NOT target the `master` branch on this repository. Instead target the next version branch. For eg. If the [current stable version](https://github.com/pratikborsadiya/vali-admin/releases/latest) of the theme is `2.3.1` then target the branch for the next version `2.3.2` or `3.0.0` if the next version is a major release. Your changes will be merged in master branch when the next release will happen.

	3.2. Give a descriptive title to your PR.

	3.3. Provide a description of your changes.

	3.4. Put `closes #XXXX` in your comment to auto-close the issue that your PR fixes.

IMPORTANT: Please review the [CONTRIBUTING.md](../CONTRIBUTING.md) file for detailed contributing guidelines.

**PLEASE REMOVE THIS TEMPLATE BEFORE SUBMITTING**