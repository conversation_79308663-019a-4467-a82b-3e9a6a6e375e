extends layouts/_layout.pug

block variables
	- var activePage = 'ui-widgets';
	- var activeGroup = 'ui-elements';

block title
	title Widgets - Vali Admin

block content
	.app-title
		div
			h1
				i.bi.bi-archive
				|  Widgets
			p Widgets to interactively display data

		ul.app-breadcrumb.breadcrumb
			li.breadcrumb-item
				i.bi.bi-house-door.fs-6
			li.breadcrumb-item
				| UI
			li.breadcrumb-item
				a(href='#') Widgets

	.row
		.col-md-3
			.widget-small.primary
				i.icon.bi.bi-people.fs-1
				div.info
					h4 Users
					p
						b 5

		.col-md-3
			.widget-small.info
				i.icon.bi.bi-heart.fs-1
				div.info
					h4 Likes
					p
						b 25

		.col-md-3
			.widget-small.warning
				i.icon.bi.bi-folder2.fs-1
				div.info
					h4 Uploades
					p
						b 10

		.col-md-3
			.widget-small.danger
				i.icon.bi.bi-star.fs-1
				div.info
					h4 Stars
					p
						b 500

	.row
		.col-md-3
			.widget-small.primary.coloured-icon
				i.icon.bi.bi-people.fs-1
				div.info
					h4 Users
					p
						b 5

		.col-md-3
			.widget-small.info.coloured-icon
				i.icon.bi.bi-heart.fs-1
				div.info
					h4 Likes
					p
						b 25

		.col-md-3
			.widget-small.warning.coloured-icon
				i.icon.bi.bi-folder2.fs-1
				div.info
					h4 Uploades
					p
						b 10

		.col-md-3
			.widget-small.danger.coloured-icon
				i.icon.bi.bi-star.fs-1
				div.info
					h4 Stars
					p
						b 500

	.row
		.col-md-6
			.tile
				h3.tile-title Chat
				.messanger
					.messages
						.message
							img(src="https://randomuser.me/api/portraits/men/4.jpg")
							p.info Hello there!
								br
								| Good Morning

						.message.me
							img(src="https://randomuser.me/api/portraits/men/1.jpg")
							p.info Hi
								br
								| Good Morning

						.message
							img(src="https://randomuser.me/api/portraits/men/4.jpg")
							p.info How are you?

						.message.me
							img(src="https://randomuser.me/api/portraits/men/1.jpg")
							p.info I'm Fine.

					.sender
						input(type="text",placeholder="Send Message")
						button.btn.btn-primary(type="button")
							i.bi.bi-send.fs-5

