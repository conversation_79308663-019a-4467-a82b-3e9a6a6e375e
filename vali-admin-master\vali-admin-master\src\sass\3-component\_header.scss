/*----- Componant: Top Navigation Bar ----- */

.app-header {
	position: fixed;
	left: 0;
	right: 0;
	top: 0;
	width: 100%;
	display: flex;
	background-color: $primary-color;
	z-index: 1030;
	padding-right: 15px;
	@media(min-width: 768px) { padding-right: 30px }
	@media print { display: none; }
}

.app-header__logo {
	flex: 1 0 auto;
	color: #fff;
	text-align: center;
	font-family: 'Niconne';
	padding: 0 15px;
	font-size: 26px;
	font-weight: 400;
	line-height: 50px;
	text-decoration: none;
	@media(min-width: 768px) {
		flex: 0 0 auto;
		display: block;
		width: $sidebar-width;
		@if $sidebar-accent == dark {
			background-color: darken( $primary-color, 5% );
		}
		@if $sidebar-accent == light {
			background-color: lighten( $primary-color, 5% );
		}
	}
	&:focus,
	&:hover {
		text-decoration: none;
		color: #fff;
	}
}

.app-sidebar__toggle {
	padding: 0 15px;
	font-family: bootstrap-icons !important;
	color: #fff;
	line-height: 2.4;
	transition: background-color 0.3s ease;
	text-decoration: none;
	@media(max-width: 767px) { order: -1; }
	&:before {
		content: "\f479";
		font-size: 21px;
	}
	&:focus,
	&:hover {
		color: #fff;
		background-color: darken($primary-color, 10);
		text-decoration: none;
	}
}

.app-nav {
	@extend .list-unstyled;
	display: flex;
	margin-bottom: 0;
	justify-content: flex-end;
	@media(min-width: 768px) { flex: 1 0 auto; }
}

.app-nav__item {
	display: block;
	padding: 15px;
	line-height: 20px;
	color: #fff;
	transition: background-color 0.3s ease;
	&:hover,
	&:focus {
		background: rgba(0, 0, 0, 0.1);
		color: #f6f6f6;
	}
}

.app-search {
	position: relative;
	display: flex;
	align-self: center;
	margin-right: 15px;
	padding: 10px 0;
	@media(max-width: 480px) { display: none; }
}

.app-search__input {
	border: 0;
	padding: 5px 10px;
	padding-right: 30px;
	border-radius: 2px;
	background-color: rgba(255,255,255,0.8);
	transition: background-color 0.3s ease;
	&::placeholder { color: rgba(0,0,0,0.4); }
}
.app-search__button {
	position: absolute;
	right: 0;
	top: 10px;
	bottom: 10px;
	padding: 0 10px;
	border: 0;
	color: rgba(0,0,0,0.8);
	background: none;
	cursor: pointer;
}

.app-notification { min-width: 270px; }
.app-notification__title {
	padding: 8px 20px;
	text-align: center;
	background-color: transparentize($primary-color, 0.6);
	color: #333;
}
.app-notification__footer {
	padding: 8px 20px;
	text-align: center;
    background-color: #eee;
	> a { text-decoration: none; }
}
.app-notification__content {
	max-height: 220px;
	overflow-y: auto;
	&::-webkit-scrollbar { width: 6px; }
	&::-webkit-scrollbar-thumb { background: rgba(0,0,0,0.2); }
}
.app-notification__item {
	display: flex;
	padding: 8px 20px;
	color: inherit;
	border-bottom: 1px solid #ddd;
	transition: background-color 0.3s ease;
	text-decoration: none;
	&:focus,
	&:hover {
		color: inherit;
		text-decoration: none;
		background-color: #e0e0e0;
	}
}

.app-notification__message,
.app-notification__meta {
	margin-bottom: 0;
}

.app-notification__icon { padding-right: 10px; }
.app-notification__message { line-height: 1.2; }
.app-notification__meta { @extend .text-muted; }
