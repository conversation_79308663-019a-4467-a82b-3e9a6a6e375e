// Componant: Widget

@use "sass:map";

.widget-small {
	display: flex;
	border-radius: 4px;
	color: $white;
	margin-bottom: 30px;
	box-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);

	.icon {
		display: flex;
		min-width: 85px;
		align-items: center;
		justify-content: center;
		padding: 20px;
		background-color: rgba(0, 0, 0, 0.2);
		border-radius: 4px 0 0 4px;
		font-size: 2.5rem;
	}

	.info {
		flex: 1;
		padding: 0 20px;
		align-self: center;

		h4 {
			text-transform: uppercase;
			margin: 0;
			margin-bottom: 5px;
			font-weight: 400;
			font-size: 1.1rem;
		}

		p {
			margin: 0;
			font-size: 16px;
		}
	}

	&.primary {
		background-color: $primary-color;

		&.coloured-icon {
			background-color: #fff;
			color: #2a2a2a;

			.icon {
				background-color: $primary-color;
				color: #fff;
			}
		}
	}

	&.info {
		background-color: map.get($theme-colors, "info");

		&.coloured-icon {
			background-color: #fff;
			color: #2a2a2a;

			.icon {
				background-color: map.get($theme-colors, "info");
				color: #fff;
			}
		}
	}

	&.warning {
		background-color: map.get($theme-colors,"warning");

		&.coloured-icon {
			background-color: #fff;
			color: #2a2a2a;

			.icon {
				background-color: map.get($theme-colors,"warning");
				color: #fff;
			}
		}
	}

	&.danger {
		background-color: map.get($theme-colors,"danger");

		&.coloured-icon {
			background-color: #fff;
			color: #2a2a2a;

			.icon {
				background-color: map.get($theme-colors,"danger");
				color: #fff;
			}
		}
	}
}