.app-sidebar__overlay(data-toggle="sidebar")
aside.app-sidebar

	.app-sidebar__user
		img.app-sidebar__user-avatar(src='https://randomuser.me/api/portraits/men/1.jpg', alt='User Image')
		div
			p.app-sidebar__user-name <PERSON>
			p.app-sidebar__user-designation Frontend Developer

	ul.app-menu
		li
			a.app-menu__item(href='dashboard.html' class={'active': activePage == 'dashboard'})
				i.app-menu__icon.bi.bi-speedometer
				span.app-menu__label Dashboard

		li.treeview(class={'is-expanded': activeGroup == 'ui-elements'})
			a.app-menu__item(href='#', data-toggle='treeview')
				i.app-menu__icon.bi.bi-laptop
				span.app-menu__label UI Elements
				i.treeview-indicator.bi.bi-chevron-right
			ul.treeview-menu
				li
					a.treeview-item(href='bootstrap-components.html' class={'active': activePage == 'ui-bootstrap'})
						i.icon.bi.bi-circle-fill
						|  Bootstrap Elements
				li
					a.treeview-item(href='https://icons.getbootstrap.com/', target="_blank", rel="noopener")
						i.icon.bi.bi-circle-fill
						|  Font Icons
				li
					a.treeview-item(href='ui-cards.html' class={'active': activePage == 'ui-cards'})
						i.icon.bi.bi-circle-fill
						|  Cards
				li
					a.treeview-item(href='widgets.html' class={'active': activePage == 'ui-widgets'})
						i.icon.bi.bi-circle-fill
						|  Widgets

		li.treeview(class={'is-expanded': activeGroup == 'forms'})
			a.app-menu__item(href='#' data-toggle='treeview')
				i.app-menu__icon.bi.bi-ui-checks
				span.app-menu__label Forms
				i.treeview-indicator.bi.bi-chevron-right
			ul.treeview-menu
				li
					a.treeview-item(href='form-components.html' class={'active': activePage == 'form-components'})
						i.icon.bi.bi-circle-fill
						|  Form Components
				li
					a.treeview-item(href='form-samples.html' class={'active': activePage == 'form-samples'})
						i.icon.bi.bi-circle-fill
						|  Form Samples

		li.treeview(class={'is-expanded': activeGroup == 'tables'})
			a.app-menu__item(href='#' data-toggle='treeview')
				i.app-menu__icon.bi.bi-table
				span.app-menu__label Tables
				i.treeview-indicator.bi.bi-chevron-right
			ul.treeview-menu
				li
					a.treeview-item(href='table-basic.html' class={'active': activePage == 'basic-table'})
						i.icon.bi.bi-circle-fill
						|  Basic Tables
				li
					a.treeview-item(href='table-data-table.html' class={'active': activePage == 'data-table'})
						i.icon.bi.bi-circle-fill
						|  Data Tables

		li.treeview(class={'is-expanded': activeGroup == 'pages'})
			a.app-menu__item(href='#' data-toggle='treeview')
				i.app-menu__icon.bi.bi-file-earmark
				span.app-menu__label Pages
				i.treeview-indicator.bi.bi-chevron-right
			ul.treeview-menu
				li
					a.treeview-item(href='blank-page.html' class={'active': activePage == 'blank-page'})
						i.icon.bi.bi-circle-fill
						|  Blank Page
				li
					a.treeview-item(href='page-login.html')
						i.icon.bi.bi-circle-fill
						|  Login Page
				li
					a.treeview-item(href='page-lockscreen.html')
						i.icon.bi.bi-circle-fill
						|  Lockscreen Page
				li
					a.treeview-item(href='page-user.html' class={'active': activePage == 'user-page'})
						i.icon.bi.bi-circle-fill
						|  User Page
				li
					a.treeview-item(href='page-invoice.html' class={'active': activePage == 'invoice-page'})
						i.icon.bi.bi-circle-fill
						|  Invoice Page
				li
					a.treeview-item(href='page-mailbox.html' class={'active': activePage == 'mailbox-page'})
						i.icon.bi.bi-circle-fill
						|  Mailbox
				li
					a.treeview-item(href='page-error.html' class={'active': activePage == 'error-page'})
						i.icon.bi.bi-circle-fill
						|  Error Page

		li
			a.app-menu__item(href='docs.html' class={'active': activePage == 'docs'})
				i.app-menu__icon.bi.bi-code-square
				span.app-menu__label Docs