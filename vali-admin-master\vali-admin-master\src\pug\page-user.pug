extends layouts/_layout.pug

block variables
	- var activePage = 'user-page';
	- var activeGroup = 'pages';

block title
	title User Profile - Vali Admin

block content

	.row.user
		.col-md-12
			.profile
				.info
					img.user-img(src="https://randomuser.me/api/portraits/men/1.jpg")
					h4 <PERSON>
					p FrontEnd Developer
				.cover-image

		.col-md-3
			.tile.p-0
				ul.nav.flex-column.nav-tabs.user-tabs
					li.nav-item
						a.nav-link.active(href='#user-timeline' data-bs-toggle='tab') Timeline
					li.nav-item
						a.nav-link(href='#user-settings' data-bs-toggle='tab') Settings

		.col-md-9
			.tab-content
				#user-timeline.tab-pane.active
					.timeline-post
						.post-media
							a(href="#")
								img(src="https://randomuser.me/api/portraits/men/1.jpg")
							.content
								h5: a(href="#") <PERSON>
								p.text-muted
									small 2 January at 9:30
						.post-content
							p Lorem ipsum dolor sit amet, consectetur adipisicing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam,	quis tion ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non	proident, sunt in culpa qui officia deserunt mollit anim id est laborum.
						
						ul.post-utility
							li.likes
								a(href="#")
									i.bi.bi-heart.me-1
									| Like

							li.shares
								a(href="#")
									i.bi.bi-share.me-1
									| Share

							li.comments
								i.bi.bi-chat-square-text.me-1
								|  5 Comments

					.timeline-post
						.post-media
							a(href="#")
								img(src="https://randomuser.me/api/portraits/men/1.jpg")
							.content
								h5: a(href="#") John Doe
								p.text-muted
									small 2 January at 9:30
						.post-content
							p Lorem ipsum dolor sit amet, consectetur adipisicing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam,	quis tion ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non	proident, sunt in culpa qui officia deserunt mollit anim id est laborum.
						
						ul.post-utility
							li.likes
								a(href="#")
									i.bi.bi-heart.me-1
									| Like

							li.shares
								a(href="#")
									i.bi.bi-share.me-1
									| Share

							li.comments
								i.bi.bi-chat-square-text.me-1
								|  5 Comments

				#user-settings.tab-pane.fade
					.tile.user-settings
						h4.line-head Settings

						form
							.row.mb-4
								.col-md-4
									label First Name
									input.form-control(type="text")

								.col-md-4
									label Last Name
									input.form-control(type="text")

							.row
								.col-md-8.mb-4
									label Email
									input.form-control(type="text")

								.clearfix

								.col-md-8.mb-4
									label Mobile No
									input.form-control(type="text")

								.clearfix

								.col-md-8.mb-4
									label Office Phone
									input.form-control(type="text")

								.clearfix
								
								.col-md-8.mb-4
									label Home Phone
									input.form-control(type="text")

							.row.mb-10
								.col-md-12
									button.btn.btn-primary(type="button")
										i.bi.bi-check-circle-fill.me-2
										|  Save


