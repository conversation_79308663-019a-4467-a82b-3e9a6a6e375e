<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use Illuminate\Support\Facades\Hash;


class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     *
     * @return void
     */
    public function run()
    {
            User::create([
        'name' => 'Admin',
        'email' => '<EMAIL>',
        'password' => Hash::make('password'), // Hashed password
        'is_admin' => true,
    ]);
        // \App\Models\User::factory(10)->create();
    }
}
