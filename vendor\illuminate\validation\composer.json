{"name": "illuminate/validation", "description": "The Illuminate Validation package.", "license": "MIT", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "require": {"php": "^8.0.2", "ext-filter": "*", "ext-mbstring": "*", "brick/math": "^0.9.3|^0.10.2|^0.11", "egulias/email-validator": "^3.2.1|^4.0", "illuminate/collections": "^9.0", "illuminate/container": "^9.0", "illuminate/contracts": "^9.0", "illuminate/macroable": "^9.0", "illuminate/support": "^9.0", "illuminate/translation": "^9.0", "symfony/http-foundation": "^6.0", "symfony/mime": "^6.0"}, "autoload": {"psr-4": {"Illuminate\\Validation\\": ""}}, "extra": {"branch-alias": {"dev-master": "9.x-dev"}}, "suggest": {"illuminate/database": "Required to use the database presence verifier (^9.0)."}, "config": {"sort-packages": true}, "minimum-stability": "dev"}