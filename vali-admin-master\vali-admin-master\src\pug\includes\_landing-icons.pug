svg(aria-hidden='true', style='position: absolute; width: 0; height: 0; overflow: hidden;', version='1.1', xmlns='http://www.w3.org/2000/svg', xmlns:xlink='http://www.w3.org/1999/xlink')
	defs
		symbol#icon-chart(viewbox='0 0 32 32')
			title chart
			path(stroke-linejoin='round', stroke-linecap='round', stroke-miterlimit='4', stroke-width='2.6667', d='M24 26.667v-13.333')
			path(stroke-linejoin='round', stroke-linecap='round', stroke-miterlimit='4', stroke-width='2.6667', d='M16 26.667v-21.333')
			path(stroke-linejoin='round', stroke-linecap='round', stroke-miterlimit='4', stroke-width='2.6667', d='M8 26.667v-8')
		symbol#icon-globe(viewbox='0 0 32 32')
			title globe
			path(stroke-linejoin='round', stroke-linecap='round', stroke-miterlimit='4', stroke-width='2.6667', d='M29.333 16c0 7.364-5.97 13.333-13.333 13.333s-13.333-5.97-13.333-13.333c0-7.364 5.97-13.333 13.333-13.333s13.333 5.97 13.333 13.333z')
			path(stroke-linejoin='round', stroke-linecap='round', stroke-miterlimit='4', stroke-width='2.6667', d='M2.667 16h26.667')
			path(stroke-linejoin='round', stroke-linecap='round', stroke-miterlimit='4', stroke-width='2.6667', d='M16 2.667c3.216 3.512 5.223 8.178 5.333 13.311l0 0.022c-0.111 5.155-2.117 9.822-5.347 13.349l0.014-0.015c-3.216-3.512-5.223-8.178-5.333-13.311l-0-0.022c0.111-5.155 2.117-9.822 5.347-13.349l-0.014 0.015z')
		symbol#icon-grid(viewbox='0 0 32 32')
			title grid
			path(stroke-linejoin='round', stroke-linecap='round', stroke-miterlimit='4', stroke-width='2.6667', d='M4 4h9.333v9.333h-9.333v-9.333z')
			path(stroke-linejoin='round', stroke-linecap='round', stroke-miterlimit='4', stroke-width='2.6667', d='M18.667 4h9.333v9.333h-9.333v-9.333z')
			path(stroke-linejoin='round', stroke-linecap='round', stroke-miterlimit='4', stroke-width='2.6667', d='M18.667 18.667h9.333v9.333h-9.333v-9.333z')
			path(stroke-linejoin='round', stroke-linecap='round', stroke-miterlimit='4', stroke-width='2.6667', d='M4 18.667h9.333v9.333h-9.333v-9.333z')
		symbol#icon-layout(viewbox='0 0 32 32')
			title layout
			path(stroke-linejoin='round', stroke-linecap='round', stroke-miterlimit='4', stroke-width='2.6667', d='M6.667 4h18.667c1.473 0 2.667 1.194 2.667 2.667v18.667c0 1.473-1.194 2.667-2.667 2.667h-18.667c-1.473 0-2.667-1.194-2.667-2.667v-18.667c0-1.473 1.194-2.667 2.667-2.667z')
			path(stroke-linejoin='round', stroke-linecap='round', stroke-miterlimit='4', stroke-width='2.6667', d='M4 12h24')
			path(stroke-linejoin='round', stroke-linecap='round', stroke-miterlimit='4', stroke-width='2.6667', d='M12 28v-16')
		symbol#icon-smartphone(viewbox='0 0 32 32')
			title smartphone
			path(stroke-linejoin='round', stroke-linecap='round', stroke-miterlimit='4', stroke-width='2.6667', d='M9.333 2.667h13.333c1.473 0 2.667 1.194 2.667 2.667v21.333c0 1.473-1.194 2.667-2.667 2.667h-13.333c-1.473 0-2.667-1.194-2.667-2.667v-21.333c0-1.473 1.194-2.667 2.667-2.667z')
			path(stroke-linejoin='round', stroke-linecap='round', stroke-miterlimit='4', stroke-width='2.6667', d='M16 24v0')
		symbol#icon-zap(viewbox='0 0 32 32')
			title zap
			path(stroke-linejoin='round', stroke-linecap='round', stroke-miterlimit='4', stroke-width='2.6667', d='M17.333 2.667l-13.333 16h12l-1.333 10.667 13.333-16h-12l1.333-10.667z')
