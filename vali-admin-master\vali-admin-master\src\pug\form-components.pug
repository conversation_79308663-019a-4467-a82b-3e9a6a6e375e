extends layouts/_layout.pug

block variables
	- var activePage = 'form-components';
	- var activeGroup = 'forms';

block title
	title Form Components - Vali Admin

block content
	.app-title
		div
			h1
				i.bi.bi-ui-checks
				|  Form Components
			p Bootstrap default form components

		ul.app-breadcrumb.breadcrumb
			li.breadcrumb-item
				i.bi.bi-house-door.fs-6
			li.breadcrumb-item
				| Forms
			li.breadcrumb-item
				a(href="#") Form Components

	.row
		.col-md-12
			.tile
				.row
					.col-lg-6
						form
							.mb-3
								label.form-label(for='exampleInputEmail1') Email address
								input#exampleInputEmail1.form-control(type='email', aria-describedby='emailHelp', placeholder='Enter email')
								small#emailHelp.form-text.text-muted We'll never share your email with anyone else.
							.mb-3
								label.form-label(for='exampleInputPassword1') Password
								input#exampleInputPassword1.form-control(type='password', placeholder='Password')
							.mb-3
								label.form-label(for='exampleSelect1') Example select
								select#exampleSelect1.form-control
									option 1
									option 2
									option 3
									option 4
									option 5
							.mb-3
								label.form-label(for='exampleSelect2') Example multiple select
								select#exampleSelect2.form-control(multiple='')
									option 1
									option 2
									option 3
									option 4
									option 5
							.mb-3
								label.form-label(for='exampleTextarea') Example textarea
								textarea#exampleTextarea.form-control(rows='3')
							.mb-3
								label.form-label(for='exampleInputFile') File input
								input#exampleInputFile.form-control(type='file', aria-describedby='fileHelp')
								div.form-text.text-muted
									| This is some placeholder block-level help text for the above input. It's a bit lighter and easily wraps to a new line.
							fieldset.mb-3
								legend Radio buttons
								.form-check
									label.form-check-label
										input#optionsRadios1.form-check-input(type='radio', name='optionsRadios', value='option1', checked='')
										| Option one is this and that—be sure to include why it's great
								.form-check
									label.form-check-label
										input#optionsRadios2.form-check-input(type='radio', name='optionsRadios', value='option2')
										| Option two can be something else and selecting it will deselect option one
								.form-check.disabled
									label.form-check-label
										input#optionsRadios3.form-check-input(type='radio', name='optionsRadios', value='option3', disabled)
										| Option three is disabled
							.form-check
								label.form-check-label
									input.form-check-input(type='checkbox')
									| Check me out

					.col-lg-4.offset-lg-1
						form
							.mb-3
								fieldset(disabled='')
									label.form-label(for='disabledInput') Disabled input
									input#disabledInput.form-control(type='text', placeholder='Disabled input here...', disabled='')
							.mb-3
								fieldset
									label.form-label(for='readOnlyInput') Readonly input
									input#readOnlyInput.form-control(type='text', placeholder='Readonly input here…', readonly='')
							.mb-3.has-success
								label.form-form-label(for='inputSuccess1') Valid input
								input#inputValid.form-control.is-valid(type='text')
								.form-control-feedback Success! You've done it.
							.mb-3.has-danger
								label.form-form-label(for='inputDanger1') Invalid input
								input#inputInvalid.form-control.is-invalid(type='text')
								.form-control-feedback Sorry, that username's taken. Try another?
							.mb-3
								label.col-form-label.col-form-label-lg(for='inputLarge') Large input
								input#inputLarge.form-control.form-control-lg(type='text')
							.mb-3
								label.col-form-label(for='inputDefault') Default input
								input#inputDefault.form-control(type='text')
							.mb-3
								label.col-form-label.col-form-label-sm(for='inputSmall') Small input
								input#inputSmall.form-control.form-control-sm(type='text')
							.mb-3
								label.form-label Input addons
								.mb-3
									label.sr-only(for='exampleInputAmount') Amount (in dollars)
									.input-group
										.input-group-prepend
											span.input-group-text $
										input#exampleInputAmount.form-control(type='text', placeholder='Amount')
										.input-group-append
											span.input-group-text .00
				.tile-footer
					button.btn.btn-primary(type='submit') Submit