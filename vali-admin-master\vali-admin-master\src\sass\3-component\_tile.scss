// Componant: Card

.tile {
	position: relative;
	background: #ffffff;
	border-radius: 3px;
	padding: 20px;
	box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 1px 5px 0 rgba(0, 0, 0, 0.12), 0 3px 1px -2px rgba(0, 0, 0, 0.2);
	margin-bottom: 30px;
	transition: all 0.3s ease-in-out;

	@media print {
		border: 1px solid #ddd;
		box-shadow: none;
	}

	&.small { padding: 0; }

	.overlay {
		position: absolute;
		top: 0;
		left: 0;
		bottom: 0;
		right: 0;
		background-color: rgba(255, 255, 255, 0.9);
		display: flex;
		align-items: center;
		justify-content: center;
		flex-direction: row;
		color: #555;
		border-radius: 3px;

		.l-text {
			font-size: 24px;
			margin-top: 15px;
			margin-left: 12px;
		}
	}

	.tile-title {
		margin-top: 0;
		margin-bottom: 20px;
		font-size: 1.5rem;
	}

	.tile-title-w-btn {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 20px;

		p { margin: 0; }

		.title { margin: 0; }

		.side {
			margin-bottom: 0;
			padding-left: 0;
		}

		.btn-group { flex: 0 0 auto; }
	}

	.tile-footer {
		border-top: 1px solid #ddd;
		padding: 20px 0 0 0;
		margin-top: 10px;
	}
}