{"name": "illuminate/events", "description": "The Illuminate Events package.", "license": "MIT", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "require": {"php": "^8.0.2", "illuminate/bus": "^9.0", "illuminate/collections": "^9.0", "illuminate/container": "^9.0", "illuminate/contracts": "^9.0", "illuminate/macroable": "^9.0", "illuminate/support": "^9.0"}, "autoload": {"psr-4": {"Illuminate\\Events\\": ""}, "files": ["functions.php"]}, "extra": {"branch-alias": {"dev-master": "9.x-dev"}}, "config": {"sort-packages": true}, "minimum-stability": "dev"}