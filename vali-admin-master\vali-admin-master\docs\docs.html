<!DOCTYPE html>
<html lang="en">
  <head>
    <meta name="description" content="<PERSON>i is a responsive and free admin theme built with Bootstrap 5, SASS and PUG.js. It's fully customizable and modular.">
    <!-- Twitter meta-->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:site" content="@pratikborsadiya">
    <meta property="twitter:creator" content="@pratikborsadiya">
    <!-- Open Graph Meta-->
    <meta property="og:type" content="website">
    <meta property="og:site_name" content="Vali Admin">
    <meta property="og:title" content="Vali - Free Bootstrap 5 admin theme">
    <meta property="og:url" content="http://pratikborsadiya.in/blog/vali-admin">
    <meta property="og:image" content="http://pratikborsadiya.in/blog/vali-admin/hero-social.png">
    <meta property="og:description" content="Vali is a responsive and free admin theme built with Bootstrap 5, SASS and PUG.js. It's fully customizable and modular.">
    <title>Documentation - Vali Admin</title>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <!-- Main CSS-->
    <link rel="stylesheet" type="text/css" href="css/main.css">
    <!-- Font-icon css-->
    <link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">
  </head>
  <body class="app sidebar-mini">
    <!-- Navbar-->
    <header class="app-header"><a class="app-header__logo" href="index.html">Vali</a>
      <!-- Sidebar toggle button--><a class="app-sidebar__toggle" href="#" data-toggle="sidebar" aria-label="Hide Sidebar"></a>
      <!-- Navbar Right Menu-->
      <ul class="app-nav">
        <li class="app-search">
          <input class="app-search__input" type="search" placeholder="Search">
          <button class="app-search__button"><i class="bi bi-search"></i></button>
        </li>
        <!--Notification Menu-->
        <li class="dropdown"><a class="app-nav__item" href="#" data-bs-toggle="dropdown" aria-label="Show notifications"><i class="bi bi-bell fs-5"></i></a>
          <ul class="app-notification dropdown-menu dropdown-menu-right">
            <li class="app-notification__title">You have 4 new notifications.</li>
            <div class="app-notification__content">
              <li><a class="app-notification__item" href="javascript:;"><span class="app-notification__icon"><i class="bi bi-envelope fs-4 text-primary"></i></span>
                  <div>
                    <p class="app-notification__message">Lisa sent you a mail</p>
                    <p class="app-notification__meta">2 min ago</p>
                  </div></a></li>
              <li><a class="app-notification__item" href="javascript:;"><span class="app-notification__icon"><i class="bi bi-exclamation-triangle fs-4 text-warning"></i></span>
                  <div>
                    <p class="app-notification__message">Mail server not working</p>
                    <p class="app-notification__meta">5 min ago</p>
                  </div></a></li>
              <li><a class="app-notification__item" href="javascript:;"><span class="app-notification__icon"><i class="bi bi-cash fs-4 text-success"></i></span>
                  <div>
                    <p class="app-notification__message">Transaction complete</p>
                    <p class="app-notification__meta">2 days ago</p>
                  </div></a></li>
              <li><a class="app-notification__item" href="javascript:;"><span class="app-notification__icon"><i class="bi bi-envelope fs-4 text-primary"></i></span>
                  <div>
                    <p class="app-notification__message">Lisa sent you a mail</p>
                    <p class="app-notification__meta">2 min ago</p>
                  </div></a></li>
              <li><a class="app-notification__item" href="javascript:;"><span class="app-notification__icon"><i class="bi bi-exclamation-triangle fs-4 text-warning"></i></span>
                  <div>
                    <p class="app-notification__message">Mail server not working</p>
                    <p class="app-notification__meta">5 min ago</p>
                  </div></a></li>
              <li><a class="app-notification__item" href="javascript:;"><span class="app-notification__icon"><i class="bi bi-cash fs-4 text-success"></i></span>
                  <div>
                    <p class="app-notification__message">Transaction complete</p>
                    <p class="app-notification__meta">2 days ago</p>
                  </div></a></li>
            </div>
            <li class="app-notification__footer"><a href="#">See all notifications.</a></li>
          </ul>
        </li>
        <!-- User Menu-->
        <li class="dropdown"><a class="app-nav__item" href="#" data-bs-toggle="dropdown" aria-label="Open Profile Menu"><i class="bi bi-person fs-4"></i></a>
          <ul class="dropdown-menu settings-menu dropdown-menu-right">
            <li><a class="dropdown-item" href="page-user.html"><i class="bi bi-gear me-2 fs-5"></i> Settings</a></li>
            <li><a class="dropdown-item" href="page-user.html"><i class="bi bi-person me-2 fs-5"></i> Profile</a></li>
            <li><a class="dropdown-item" href="page-login.html"><i class="bi bi-box-arrow-right me-2 fs-5"></i> Logout</a></li>
          </ul>
        </li>
      </ul>
    </header>
    <!-- Sidebar menu-->
    <div class="app-sidebar__overlay" data-toggle="sidebar"></div>
    <aside class="app-sidebar">
      <div class="app-sidebar__user"><img class="app-sidebar__user-avatar" src="https://randomuser.me/api/portraits/men/1.jpg" alt="User Image">
        <div>
          <p class="app-sidebar__user-name">John Doe</p>
          <p class="app-sidebar__user-designation">Frontend Developer</p>
        </div>
      </div>
      <ul class="app-menu">
        <li><a class="app-menu__item" href="dashboard.html"><i class="app-menu__icon bi bi-speedometer"></i><span class="app-menu__label">Dashboard</span></a></li>
        <li class="treeview"><a class="app-menu__item" href="#" data-toggle="treeview"><i class="app-menu__icon bi bi-laptop"></i><span class="app-menu__label">UI Elements</span><i class="treeview-indicator bi bi-chevron-right"></i></a>
          <ul class="treeview-menu">
            <li><a class="treeview-item" href="bootstrap-components.html"><i class="icon bi bi-circle-fill"></i> Bootstrap Elements</a></li>
            <li><a class="treeview-item" href="https://icons.getbootstrap.com/" target="_blank" rel="noopener"><i class="icon bi bi-circle-fill"></i> Font Icons</a></li>
            <li><a class="treeview-item" href="ui-cards.html"><i class="icon bi bi-circle-fill"></i> Cards</a></li>
            <li><a class="treeview-item" href="widgets.html"><i class="icon bi bi-circle-fill"></i> Widgets</a></li>
          </ul>
        </li>
        <li class="treeview"><a class="app-menu__item" href="#" data-toggle="treeview"><i class="app-menu__icon bi bi-ui-checks"></i><span class="app-menu__label">Forms</span><i class="treeview-indicator bi bi-chevron-right"></i></a>
          <ul class="treeview-menu">
            <li><a class="treeview-item" href="form-components.html"><i class="icon bi bi-circle-fill"></i> Form Components</a></li>
            <li><a class="treeview-item" href="form-samples.html"><i class="icon bi bi-circle-fill"></i> Form Samples</a></li>
          </ul>
        </li>
        <li class="treeview"><a class="app-menu__item" href="#" data-toggle="treeview"><i class="app-menu__icon bi bi-table"></i><span class="app-menu__label">Tables</span><i class="treeview-indicator bi bi-chevron-right"></i></a>
          <ul class="treeview-menu">
            <li><a class="treeview-item" href="table-basic.html"><i class="icon bi bi-circle-fill"></i> Basic Tables</a></li>
            <li><a class="treeview-item" href="table-data-table.html"><i class="icon bi bi-circle-fill"></i> Data Tables</a></li>
          </ul>
        </li>
        <li class="treeview"><a class="app-menu__item" href="#" data-toggle="treeview"><i class="app-menu__icon bi bi-file-earmark"></i><span class="app-menu__label">Pages</span><i class="treeview-indicator bi bi-chevron-right"></i></a>
          <ul class="treeview-menu">
            <li><a class="treeview-item" href="blank-page.html"><i class="icon bi bi-circle-fill"></i> Blank Page</a></li>
            <li><a class="treeview-item" href="page-login.html"><i class="icon bi bi-circle-fill"></i> Login Page</a></li>
            <li><a class="treeview-item" href="page-lockscreen.html"><i class="icon bi bi-circle-fill"></i> Lockscreen Page</a></li>
            <li><a class="treeview-item" href="page-user.html"><i class="icon bi bi-circle-fill"></i> User Page</a></li>
            <li><a class="treeview-item" href="page-invoice.html"><i class="icon bi bi-circle-fill"></i> Invoice Page</a></li>
            <li><a class="treeview-item" href="page-mailbox.html"><i class="icon bi bi-circle-fill"></i> Mailbox</a></li>
            <li><a class="treeview-item" href="page-error.html"><i class="icon bi bi-circle-fill"></i> Error Page</a></li>
          </ul>
        </li>
        <li><a class="app-menu__item active" href="docs.html"><i class="app-menu__icon bi bi-code-square"></i><span class="app-menu__label">Docs</span></a></li>
      </ul>
    </aside>
    <main class="app-content">
      <div class="app-title">
        <div>
          <h1><i class="bi bi-code-square"></i> Documentation</h1>
          <p>Documentation of vali admin</p>
        </div>
        <ul class="app-breadcrumb breadcrumb">
          <li class="breadcrumb-item"><i class="bi bi-house-door fs-6"></i></li>
          <li class="breadcrumb-item"><a href="#">Documentation</a></li>
        </ul>
      </div>
      <div class="tile">
        <div class="tile-body">
          <div class="docs" style="max-width: 700px;">
            <h2 class="docs-title">Directory Structure</h2>
            <pre class="directory-structure">│
├── docs - <i>compiled files</i>
│   ├── css
│   ├── images
│   └── js
└── src - <i>Layout and style source files</i>
&nbsp;&nbsp;&nbsp;&nbsp;├── pug - <i>Layout source</i>
&nbsp;&nbsp;&nbsp;&nbsp;└── sass - <i>Style source</i>
</pre>
            <h2 class="docs-title" id="Compilation-of-source-files">Compilation of source files</h2>
            <p>The theme is built using SASS and PugJs which are in turn compiled into HTML and CSS by Grunt. If you are not familiar with Grunt, <a href="https://24ways.org/2013/grunt-is-not-weird-and-hard/" target="_blank" rel="noopener">here</a> is an article to get started. If you are familiar with Grunt follow the instruction mentioned bellow to edit or customize the source. </p>
            <p>If you don't want to edit theme you can use the compiled files directly inside <code>docs</code> folder.</p>
            <p>Run <code>npm install</code> command in project root directory to install and build dependencies.</p>
            <p>Use <code>npm run dev</code> task to edit and compile source files on the go or use <code>npm run build</code> task to compile all source files at once.</p>
            <h2 class="docs-title" id="Layout-Customization">Layout Customization</h2>
            <p>The layout is built using PugJs. All the layout source files are located in <code>src/pug</code> directory. There are two sub directories inside this directory: 
              <ol>
                <li><code>layout</code> - Includes common HTML skeleton layout which is extended by all the pages</li>
                <li><code>includes</code> - Includes layout partials like sidebar and navbar and footer</li>
              </ol>
            </p>
            <h2 class="docs-title" id="Style-Customization">Style Customization</h2>
            <p>The styles are written in SASS. All the style files are located in <code>src/sass</code> directory. There is a file in this directory <code>main.sass</code> which imports all the files and exported as main.css There are four sub directories inside this directory:
              <ol>
                <li><code>1-tools</code> - It includes styles of all the external libraries and a file <code>_var.scss</code> which contains the variables required for the application</li>
                <li><code>2-basics</code> - It contains the basic style like overall structure css and theming options</li>
                <li><code>3-component</code> - It contains the styles for the components like card, widgets, sidebar, navbar etc</li>
                <li><code>4-pages</code> - It contains the styles for the specific pages like login page, lock-screen page </li>
              </ol>
            </p>
            <p>To customize the primary color of the theme and sidebar you need to change the variables in the <code>1-tools/_var.scss</code>. The detailed documentation about changing the colors is mentioned in this file itself.</p>
            <p>If you don't want to use particular component or plug-in just comment the import statement for that particular component in <code>src/sass/main.scss</code> and compile the SASS by running <code>npm run build</code> command.</p>
            <h2 class="docs-title" id="Compatibility-with-other-frameworks">Compatibility with other frameworks</h2>
            <p>This theme is not built for a specific framework or technology like Angular or React etc. But due to it's modular nature it's very easy to incorporate it into any front-end or back-end framework like Angular, React or VueJs or Node JS. The CSS is modular enough to be incorporated in any framework. While the Javascript used to make the components interactive can be used from any of the following framework.</p>
            <p>If you are using Angular you can use <a href="https://angular-ui.github.io/bootstrap/" rel="noopener">ui-bootstrap</a>, for React use <a href="https://react-bootstrap.github.io/" rel="noopener">React-Bootstrap</a> and for VueJs you can use <a href="https://yuche.github.io/vue-strap/" rel="noopener">VueStrap</a>.</p>
            <p>If you are using Node JS as your web server you can use pug as your layout engine to render html templates as is without compiling them to HTML. More details are available <a href="https://pugjs.org/api/express.html" target="_balnk">here</a>.</p>
            <h2 class="docs-title" id="RTL-Support">RTL Support</h2>
            <p>To enable RTL support
              <ul>
                <li>Uncomment this line <code>@import '3-component/rtl';</code> in <code>src/sass/main.scss</code>. </li>
                <li>Add <code>dir="rtl"</code> attribute to <code>&lt;html&gt;</code> tag in <code>src/pug/layouts/_layout.pug</code>.</li>
                <li>Build the source files using <code>npm run build</code> command.</li>
              </ul>
            </p>
            <h2 class="docs-title" id="Contribution-and-Issues">Contribution & Issues</h2>
            <p>If you liked the theme do star and fork it on <a href="https://github.com/pratikborsadiya/vali-admin" target="_blank" rel="noopener">GitHub</a>. If you find anything missing or want to contribute to this documentation, the source is available <a href="https://github.com/pratikborsadiya/vali-admin/blob/master/src/pug/docs.pug" target="_blank" rel="noopener">here</a>. If you have an issue or feature request regarding theme please report it <a href="https://github.com/pratikborsadiya/vali-admin/issues/new" target="_blank" rel="noopener">here</a>.</p>
          </div>
        </div>
      </div>
    </main>
    <!-- Essential javascripts for application to work-->
    <script src="js/jquery-3.7.0.min.js"></script>
    <script src="js/bootstrap.min.js"></script>
    <script src="js/main.js"></script>
    <!-- Page specific javascripts-->
    <!-- Google analytics script-->
    <script type="text/javascript">
      if(document.location.hostname == 'pratikborsadiya.in') {
      	(function(i,s,o,g,r,a,m){i['GoogleAnalyticsObject']=r;i[r]=i[r]||function(){
      	(i[r].q=i[r].q||[]).push(arguments)},i[r].l=1*new Date();a=s.createElement(o),
      	m=s.getElementsByTagName(o)[0];a.async=1;a.src=g;m.parentNode.insertBefore(a,m)
      	})(window,document,'script','//www.google-analytics.com/analytics.js','ga');
      	ga('create', 'UA-72504830-1', 'auto');
      	ga('send', 'pageview');
      }
    </script>
  </body>
</html>