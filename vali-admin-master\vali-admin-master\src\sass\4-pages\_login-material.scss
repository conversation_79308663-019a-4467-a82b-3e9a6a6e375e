// Page: Login Material

.login-content {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	min-height: 100vh;

	.logo {
		margin-bottom: 40px;
		font-family: "Niconne";
		color: #fff;

		h1 {
			font-size: 52px;
			font-weight: 400;
		}
	}

	.login-box {
		position: relative;
		min-width: 350px;
		min-height: 390px;
		background-color: #fff;
		box-shadow: 0px 29px 147.5px 102.5px rgba(0, 0, 0, 0.05), 0px 29px 95px 0px rgba(0, 0, 0, 0.16);
		perspective: 800px;
		transition: all 0.5s ease-in-out;

		.login-head {
			margin-top: 0;
			margin-bottom: 20px;
			padding-bottom: 20px;
			border-bottom: 1px solid #ddd;
			text-align: center;
		}

		label {
			color: #666;
			font-weight: 700;
		}

		.utility {
			display: flex;
			padding: 1px;
			justify-content: space-between;
			align-items: center;
		}

		.btn-container {
			text-align: center;
			margin-bottom: 0;
		}

		.login-form, .forget-form {
			position: absolute;
			top: 0;
			bottom: 0;
			left: 0;
			right: 0;
			padding: 40px;
			backface-visibility: hidden;
			-webkit-backface-visibility: hidden;
			transition: all 0.5s ease-in-out;
		}

		.forget-form {
			opacity: 0;
			transform: rotateY(180deg);
		}

		&.flipped {
			min-height: 300px;

			.login-form {
				opacity: 0;
				transform: rotateY(-180deg);
			}

			.forget-form {
				opacity: 1;
				transform: rotateY(0deg);
			}
		}
	}
}

@media (max-width: 351px) {
	.login-content {
		.login-box {
			min-width: 100%;

			.login-form, .forget-form {
				width: 100%;
			}
		}
	}
}