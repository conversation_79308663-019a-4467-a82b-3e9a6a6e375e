{"name": "illuminate/support", "description": "The Illuminate Support package.", "license": "MIT", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "require": {"php": "^8.0.2", "ext-ctype": "*", "ext-filter": "*", "ext-mbstring": "*", "doctrine/inflector": "^2.0", "illuminate/collections": "^9.0", "illuminate/conditionable": "^9.0", "illuminate/contracts": "^9.0", "illuminate/macroable": "^9.0", "nesbot/carbon": "^2.62.1", "voku/portable-ascii": "^2.0"}, "conflict": {"tightenco/collect": "<5.5.33"}, "autoload": {"psr-4": {"Illuminate\\Support\\": ""}, "files": ["helpers.php"]}, "extra": {"branch-alias": {"dev-master": "9.x-dev"}}, "suggest": {"illuminate/filesystem": "Required to use the composer class (^9.0).", "league/commonmark": "Required to use Str::markdown() and Stringable::markdown() (^2.0.2).", "ramsey/uuid": "Required to use Str::uuid() (^4.7).", "symfony/process": "Required to use the composer class (^6.0).", "symfony/uid": "Required to use Str::ulid() (^6.0).", "symfony/var-dumper": "Required to use the dd function (^6.0).", "vlucas/phpdotenv": "Required to use the Env class and env helper (^5.4.1)."}, "config": {"sort-packages": true}, "minimum-stability": "dev"}