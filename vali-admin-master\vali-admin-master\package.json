{"name": "vali-admin", "version": "4.1.0", "description": "An admin theme built with Bootstrap, sass and PugJs", "devDependencies": {"autoprefixer": "^9.8.8", "grunt": "^1.6.1", "grunt-contrib-cssmin": "^5.0.0", "grunt-contrib-pug": "^3.0.0", "grunt-contrib-watch": "^1.1.0", "grunt-postcss": "^0.9.0", "grunt-sass": "^3.1.0", "sass": "^1.69.3"}, "scripts": {"dev": "grunt watch", "build": "grunt build --verbose", "start": "node node-dev-server.js"}, "repository": {"type": "git", "url": "git+https://github.com/pratikborsadiya/vali-admin.git"}, "keywords": ["admin", "dashboard", "theme"], "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/pratikborsadiya/vali-admin/issues"}, "homepage": "http://pratikborsadiya.in/blog/vali-admin", "dependencies": {"bootstrap": "^5.3.2"}, "files": ["docs"]}