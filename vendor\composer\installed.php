<?php return array(
    'root' => array(
        'name' => '__root__',
        'pretty_version' => '1.0.0+no-version-set',
        'version' => '1.0.0.0',
        'reference' => null,
        'type' => 'library',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'dev' => true,
    ),
    'versions' => array(
        '__root__' => array(
            'pretty_version' => '1.0.0+no-version-set',
            'version' => '1.0.0.0',
            'reference' => null,
            'type' => 'library',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'brick/math' => array(
            'pretty_version' => '0.11.0',
            'version' => '0.11.0.0',
            'reference' => '0ad82ce168c82ba30d1c01ec86116ab52f589478',
            'type' => 'library',
            'install_path' => __DIR__ . '/../brick/math',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'carbonphp/carbon-doctrine-types' => array(
            'pretty_version' => '2.1.0',
            'version' => '2.1.0.0',
            'reference' => '99f76ffa36cce3b70a4a6abce41dba15ca2e84cb',
            'type' => 'library',
            'install_path' => __DIR__ . '/../carbonphp/carbon-doctrine-types',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'doctrine/deprecations' => array(
            'pretty_version' => '1.1.5',
            'version' => '1.1.5.0',
            'reference' => '459c2f5dd3d6a4633d3b5f46ee2b1c40f57d3f38',
            'type' => 'library',
            'install_path' => __DIR__ . '/../doctrine/deprecations',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'doctrine/inflector' => array(
            'pretty_version' => '2.0.10',
            'version' => '2.0.10.0',
            'reference' => '5817d0659c5b50c9b950feb9af7b9668e2c436bc',
            'type' => 'library',
            'install_path' => __DIR__ . '/../doctrine/inflector',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'doctrine/lexer' => array(
            'pretty_version' => '2.1.1',
            'version' => '2.1.1.0',
            'reference' => '861c870e8b75f7c8f69c146c7f89cc1c0f1b49b6',
            'type' => 'library',
            'install_path' => __DIR__ . '/../doctrine/lexer',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'egulias/email-validator' => array(
            'pretty_version' => '3.2.6',
            'version' => '3.2.6.0',
            'reference' => 'e5997fa97e8790cdae03a9cbd5e78e45e3c7bda7',
            'type' => 'library',
            'install_path' => __DIR__ . '/../egulias/email-validator',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'illuminate/bus' => array(
            'pretty_version' => 'v9.52.16',
            'version' => '*********',
            'reference' => '4c719a19c3d8c34b2494a7206f8ffde3eff3f983',
            'type' => 'library',
            'install_path' => __DIR__ . '/../illuminate/bus',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'illuminate/collections' => array(
            'pretty_version' => 'v9.52.16',
            'version' => '*********',
            'reference' => 'd3710b0b244bfc62c288c1a87eaa62dd28352d1f',
            'type' => 'library',
            'install_path' => __DIR__ . '/../illuminate/collections',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'illuminate/conditionable' => array(
            'pretty_version' => 'v9.52.16',
            'version' => '*********',
            'reference' => 'bea24daa0fa84b7e7b0d5b84f62c71b7e2dc3364',
            'type' => 'library',
            'install_path' => __DIR__ . '/../illuminate/conditionable',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'illuminate/console' => array(
            'pretty_version' => 'v9.52.16',
            'version' => '*********',
            'reference' => 'c794b268b9fd3c2a535c766c011fb574e51b071e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../illuminate/console',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'illuminate/container' => array(
            'pretty_version' => 'v9.52.16',
            'version' => '*********',
            'reference' => '1641dda2d0750b68bb1264a3b37ff3973f2e6265',
            'type' => 'library',
            'install_path' => __DIR__ . '/../illuminate/container',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'illuminate/contracts' => array(
            'pretty_version' => 'v9.52.16',
            'version' => '*********',
            'reference' => '44f65d723b13823baa02ff69751a5948bde60c22',
            'type' => 'library',
            'install_path' => __DIR__ . '/../illuminate/contracts',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'illuminate/events' => array(
            'pretty_version' => 'v9.52.16',
            'version' => '*********',
            'reference' => '8e534676bac23bc17925f5c74c128f9c09b98f69',
            'type' => 'library',
            'install_path' => __DIR__ . '/../illuminate/events',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'illuminate/filesystem' => array(
            'pretty_version' => 'v9.52.16',
            'version' => '*********',
            'reference' => '8168361548b2c5e2e501096cfbadb62a4a526290',
            'type' => 'library',
            'install_path' => __DIR__ . '/../illuminate/filesystem',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'illuminate/macroable' => array(
            'pretty_version' => 'v9.52.16',
            'version' => '*********',
            'reference' => 'e3bfaf6401742a9c6abca61b9b10e998e5b6449a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../illuminate/macroable',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'illuminate/pipeline' => array(
            'pretty_version' => 'v9.52.16',
            'version' => '*********',
            'reference' => 'e0be3f3f79f8235ad7334919ca4094d5074e02f6',
            'type' => 'library',
            'install_path' => __DIR__ . '/../illuminate/pipeline',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'illuminate/support' => array(
            'pretty_version' => 'v9.52.16',
            'version' => '*********',
            'reference' => '223c608dbca27232df6213f776bfe7bdeec24874',
            'type' => 'library',
            'install_path' => __DIR__ . '/../illuminate/support',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'illuminate/translation' => array(
            'pretty_version' => 'v9.52.16',
            'version' => '*********',
            'reference' => '396c09cd4a91c05b45f905cc0cf9a6c240d6ab8e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../illuminate/translation',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'illuminate/validation' => array(
            'pretty_version' => 'v9.52.16',
            'version' => '*********',
            'reference' => '07e85bd1f25d7f32cef5153ec2802a6f8451dc78',
            'type' => 'library',
            'install_path' => __DIR__ . '/../illuminate/validation',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'illuminate/view' => array(
            'pretty_version' => 'v9.52.16',
            'version' => '*********',
            'reference' => '0215165781b3269cdbecdfe63ffddd0e6cecfd6e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../illuminate/view',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'laravel/breeze' => array(
            'pretty_version' => 'v1.19.2',
            'version' => '1.19.2.0',
            'reference' => '725e0c4fb1f630afdd90b5fba2a7f6d8d547ac29',
            'type' => 'library',
            'install_path' => __DIR__ . '/../laravel/breeze',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'nesbot/carbon' => array(
            'pretty_version' => '2.73.0',
            'version' => '2.73.0.0',
            'reference' => '9228ce90e1035ff2f0db84b40ec2e023ed802075',
            'type' => 'library',
            'install_path' => __DIR__ . '/../nesbot/carbon',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'nunomaduro/termwind' => array(
            'pretty_version' => 'v1.15.1',
            'version' => '1.15.1.0',
            'reference' => '8ab0b32c8caa4a2e09700ea32925441385e4a5dc',
            'type' => 'library',
            'install_path' => __DIR__ . '/../nunomaduro/termwind',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'psr/clock' => array(
            'pretty_version' => '1.0.0',
            'version' => '1.0.0.0',
            'reference' => 'e41a24703d4560fd0acb709162f73b8adfc3aa0d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/clock',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'psr/clock-implementation' => array(
            'dev_requirement' => true,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/container' => array(
            'pretty_version' => '2.0.2',
            'version' => '2.0.2.0',
            'reference' => 'c71ecc56dfe541dbd90c5360474fbc405f8d5963',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/container',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'psr/container-implementation' => array(
            'dev_requirement' => true,
            'provided' => array(
                0 => '1.1|2.0',
            ),
        ),
        'psr/log-implementation' => array(
            'dev_requirement' => true,
            'provided' => array(
                0 => '1.0|2.0|3.0',
            ),
        ),
        'psr/simple-cache' => array(
            'pretty_version' => '3.0.0',
            'version' => '3.0.0.0',
            'reference' => '764e0b3939f5ca87cb904f570ef9be2d78a07865',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/simple-cache',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'symfony/console' => array(
            'pretty_version' => 'v6.0.19',
            'version' => '6.0.19.0',
            'reference' => 'c3ebc83d031b71c39da318ca8b7a07ecc67507ed',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/console',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'symfony/deprecation-contracts' => array(
            'pretty_version' => 'v3.0.2',
            'version' => '3.0.2.0',
            'reference' => '26954b3d62a6c5fd0ea8a2a00c0353a14978d05c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/deprecation-contracts',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'symfony/finder' => array(
            'pretty_version' => 'v6.0.19',
            'version' => '6.0.19.0',
            'reference' => '5cc9cac6586fc0c28cd173780ca696e419fefa11',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/finder',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'symfony/http-foundation' => array(
            'pretty_version' => 'v6.0.20',
            'version' => '6.0.20.0',
            'reference' => 'e16b2676a4b3b1fa12378a20b29c364feda2a8d6',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/http-foundation',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'symfony/mime' => array(
            'pretty_version' => 'v6.0.19',
            'version' => '6.0.19.0',
            'reference' => 'd7052547a0070cbeadd474e172b527a00d657301',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/mime',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'symfony/polyfill-ctype' => array(
            'pretty_version' => 'v1.32.0',
            'version' => '1.32.0.0',
            'reference' => 'a3cc8b044a6ea513310cbd48ef7333b384945638',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-ctype',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'symfony/polyfill-intl-grapheme' => array(
            'pretty_version' => 'v1.32.0',
            'version' => '1.32.0.0',
            'reference' => 'b9123926e3b7bc2f98c02ad54f6a4b02b91a8abe',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-intl-grapheme',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'symfony/polyfill-intl-idn' => array(
            'pretty_version' => 'v1.32.0',
            'version' => '1.32.0.0',
            'reference' => '9614ac4d8061dc257ecc64cba1b140873dce8ad3',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-intl-idn',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'symfony/polyfill-intl-normalizer' => array(
            'pretty_version' => 'v1.32.0',
            'version' => '1.32.0.0',
            'reference' => '3833d7255cc303546435cb650316bff708a1c75c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-intl-normalizer',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'symfony/polyfill-mbstring' => array(
            'pretty_version' => 'v1.32.0',
            'version' => '1.32.0.0',
            'reference' => '6d857f4d76bd4b343eac26d6b539585d2bc56493',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-mbstring',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'symfony/polyfill-php80' => array(
            'pretty_version' => 'v1.32.0',
            'version' => '1.32.0.0',
            'reference' => '0cc9dd0f17f61d8131e7df6b84bd344899fe2608',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-php80',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'symfony/process' => array(
            'pretty_version' => 'v6.0.19',
            'version' => '6.0.19.0',
            'reference' => '2114fd60f26a296cc403a7939ab91478475a33d4',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/process',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'symfony/service-contracts' => array(
            'pretty_version' => 'v3.0.2',
            'version' => '3.0.2.0',
            'reference' => 'd78d39c1599bd1188b8e26bb341da52c3c6d8a66',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/service-contracts',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'symfony/string' => array(
            'pretty_version' => 'v6.0.19',
            'version' => '6.0.19.0',
            'reference' => 'd9e72497367c23e08bf94176d2be45b00a9d232a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/string',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'symfony/translation' => array(
            'pretty_version' => 'v6.0.19',
            'version' => '6.0.19.0',
            'reference' => '9c24b3fdbbe9fb2ef3a6afd8bbaadfd72dad681f',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/translation',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'symfony/translation-contracts' => array(
            'pretty_version' => 'v3.0.2',
            'version' => '3.0.2.0',
            'reference' => 'acbfbb274e730e5a0236f619b6168d9dedb3e282',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/translation-contracts',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'symfony/translation-implementation' => array(
            'dev_requirement' => true,
            'provided' => array(
                0 => '2.3|3.0',
            ),
        ),
        'voku/portable-ascii' => array(
            'pretty_version' => '2.0.3',
            'version' => '2.0.3.0',
            'reference' => 'b1d923f88091c6bf09699efcd7c8a1b1bfd7351d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../voku/portable-ascii',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
    ),
);
