// Componant: RTL
// Right to left support

[dir=rtl] {
	body {
		text-align: right;
		direction: rtl;
	}

	.app-sidebar {
		left: auto;
		right: 0;
	}

	.app-sidebar__user-avatar {
		margin-left: 15px;
		margin-right: 0;
	}

	.dropdown-menu.dropdown-menu-right {
		right: auto;
		left: 0;
	}

	.list-unstyled,
	.app-nav,
	.app-menu,
	.user .user-tabs,
	.treeview-menu {
		padding-right: 0;
	}

	.btn .icon,
	.btn .bi,
	.treeview-item .icon {
		margin-right: 0;
		margin-left: 5px;
	}

	.modal-header .close {
		margin-left: 0;
		margin-right: auto;
	}

	.modal-footer > :not(:last-child) {
		margin-right: 0;
		margin-left: 0.25rem;
	}

	.widget-small .icon { border-radius: 0 4px 4px 0; }

	.user .timeline-post .post-media img {
		margin-right: 0;
		margin-left: 10px;
	}

	table.table-bordered.dataTable th:last-child,
	table.table-bordered.dataTable th:last-child,
	table.table-bordered.dataTable td:last-child,
	table.table-bordered.dataTable td:last-child {
		border-right-width: 1px;
	}

	@media(min-width: 768px) {
		.app-header {
			padding-right: 0;
			padding-left: 30px;
		}
		.app-content {
			margin-left: 0;
			margin-right: 230px;
			transition: margin-right 0.3s ease;
		}
		.sidebar-mini.sidenav-toggled .app-content {
			margin-left: 0;
			margin-right: 50px;
		}
		.sidebar-mini.sidenav-toggled .app-menu__label,
		.sidebar-mini.sidenav-toggled .treeview-menu {
			left: auto;
			right: 50px;
		}
		.sidebar-mini.sidenav-toggled .treeview .app-menu__label {
			border-top-left-radius: 4px;
			border-top-right-radius: 0;
			border-bottom-left-radius: 0;
		}
		.sidebar-mini.sidenav-toggled .treeview-menu {
			border-bottom-right-radius: 0;
			border-bottom-left-radius: 4px;
		}
		.sidebar-mini.sidenav-toggled .app-menu__label {
			border-top-right-radius: 0;
			border-bottom-right-radius: 0;
			border-top-left-radius: 4px;
			border-bottom-left-radius: 4px;
		}
	}

	@media(max-width: 768px) {
		.app.sidenav-toggled .app-sidebar {
			left: auto;
			right: 0;
		}
		.app .app-sidebar {
			left: auto;
			right: -230px;
		}
	}
}