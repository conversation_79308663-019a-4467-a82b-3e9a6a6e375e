doctype html
html(lang="en")

	//- Variables to set active page in sidebar
	block variables
		- var activePage = 'dashboard';
		- var activeGroup = 'none';

	head
		meta(name="description", content="Vali is a responsive and free admin theme built with Bootstrap 5, SASS and PUG.js. It's fully customizable and modular.")

		// Twitter meta
		meta(property="twitter:card", content="summary_large_image")
		meta(property="twitter:site", content="@pratikborsadiya")
		meta(property="twitter:creator", content="@pratikborsadiya")

		// Open Graph Meta
		meta(property="og:type", content="website")
		meta(property="og:site_name", content="Vali Admin")
		meta(property="og:title", content="Vali - Free Bootstrap 5 admin theme")
		meta(property='og:url', content='http://pratikborsadiya.in/blog/vali-admin')
		meta(property='og:image', content='http://pratikborsadiya.in/blog/vali-admin/hero-social.png')
		meta(property="og:description", content="Vali is a responsive and free admin theme built with Bootstrap 5, SASS and PUG.js. It's fully customizable and modular.")

		block title
			title Vali Admin - Free Bootstrap 5 Admin Template

		include ../includes/_head.pug

	body.app.sidebar-mini

		// Navbar
		include ../includes/_header.pug

		// Sidebar menu
		include ../includes/_sidebar.pug

		main.app-content
			block content

		//- Javascripts
		include ../includes/_javascript.pug
		
		// Page specific javascripts
		block specific-js

		// Google analytics script
		script(type="text/javascript").
			if(document.location.hostname == 'pratikborsadiya.in') {
				(function(i,s,o,g,r,a,m){i['GoogleAnalyticsObject']=r;i[r]=i[r]||function(){
				(i[r].q=i[r].q||[]).push(arguments)},i[r].l=1*new Date();a=s.createElement(o),
				m=s.getElementsByTagName(o)[0];a.async=1;a.src=g;m.parentNode.insertBefore(a,m)
				})(window,document,'script','//www.google-analytics.com/analytics.js','ga');
				ga('create', 'UA-72504830-1', 'auto');
				ga('send', 'pageview');
			}
