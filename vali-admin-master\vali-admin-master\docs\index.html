<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="Vali is a responsive and free dashboard theme/template built with Bootstrap 5, SASS and PUG.js.">
    <!-- Twitter meta-->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:site" content="@pratikborsadiya">
    <meta property="twitter:creator" content="@pratikborsadiya">
    <!-- Open Graph Meta-->
    <meta property="og:type" content="website">
    <meta property="og:site_name" content="Vali Admin">
    <meta property="og:title" content="Vali Admin - Free Bootstrap 5 Dashboard Template">
    <meta property="og:url" content="http://pratikborsadiya.in/vali-admin">
    <meta property="og:image" content="http://pratikborsadiya.in/blog/vali-admin/hero-social.png">
    <meta property="og:description" content="<PERSON>i is a responsive and free dashboard theme/template built with Bootstrap 5, SASS and PUG.js.">
    <title>Vali Admin - Free Bootstrap 5 Dashboard Template</title>
    <link rel="stylesheet" type="text/css" href="css/main.css">
  </head>
  <body class="landing-page">
    <svg aria-hidden="true" style="position: absolute; width: 0; height: 0; overflow: hidden;" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
      <defs>
        <symbol id="icon-chart" viewbox="0 0 32 32">
          <title>chart</title>
          <path stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" stroke-width="2.6667" d="M24 26.667v-13.333"></path>
          <path stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" stroke-width="2.6667" d="M16 26.667v-21.333"></path>
          <path stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" stroke-width="2.6667" d="M8 26.667v-8"></path>
        </symbol>
        <symbol id="icon-globe" viewbox="0 0 32 32">
          <title>globe</title>
          <path stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" stroke-width="2.6667" d="M29.333 16c0 7.364-5.97 13.333-13.333 13.333s-13.333-5.97-13.333-13.333c0-7.364 5.97-13.333 13.333-13.333s13.333 5.97 13.333 13.333z"></path>
          <path stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" stroke-width="2.6667" d="M2.667 16h26.667"></path>
          <path stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" stroke-width="2.6667" d="M16 2.667c3.216 3.512 5.223 8.178 5.333 13.311l0 0.022c-0.111 5.155-2.117 9.822-5.347 13.349l0.014-0.015c-3.216-3.512-5.223-8.178-5.333-13.311l-0-0.022c0.111-5.155 2.117-9.822 5.347-13.349l-0.014 0.015z"></path>
        </symbol>
        <symbol id="icon-grid" viewbox="0 0 32 32">
          <title>grid</title>
          <path stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" stroke-width="2.6667" d="M4 4h9.333v9.333h-9.333v-9.333z"></path>
          <path stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" stroke-width="2.6667" d="M18.667 4h9.333v9.333h-9.333v-9.333z"></path>
          <path stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" stroke-width="2.6667" d="M18.667 18.667h9.333v9.333h-9.333v-9.333z"></path>
          <path stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" stroke-width="2.6667" d="M4 18.667h9.333v9.333h-9.333v-9.333z"></path>
        </symbol>
        <symbol id="icon-layout" viewbox="0 0 32 32">
          <title>layout</title>
          <path stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" stroke-width="2.6667" d="M6.667 4h18.667c1.473 0 2.667 1.194 2.667 2.667v18.667c0 1.473-1.194 2.667-2.667 2.667h-18.667c-1.473 0-2.667-1.194-2.667-2.667v-18.667c0-1.473 1.194-2.667 2.667-2.667z"></path>
          <path stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" stroke-width="2.6667" d="M4 12h24"></path>
          <path stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" stroke-width="2.6667" d="M12 28v-16"></path>
        </symbol>
        <symbol id="icon-smartphone" viewbox="0 0 32 32">
          <title>smartphone</title>
          <path stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" stroke-width="2.6667" d="M9.333 2.667h13.333c1.473 0 2.667 1.194 2.667 2.667v21.333c0 1.473-1.194 2.667-2.667 2.667h-13.333c-1.473 0-2.667-1.194-2.667-2.667v-21.333c0-1.473 1.194-2.667 2.667-2.667z"></path>
          <path stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" stroke-width="2.6667" d="M16 24v0"></path>
        </symbol>
        <symbol id="icon-zap" viewbox="0 0 32 32">
          <title>zap</title>
          <path stroke-linejoin="round" stroke-linecap="round" stroke-miterlimit="4" stroke-width="2.6667" d="M17.333 2.667l-13.333 16h12l-1.333 10.667 13.333-16h-12l1.333-10.667z"></path>
        </symbol>
      </defs>
    </svg>
    <section class="landing-hero">
      <div class="landing-hero-overlay"></div>
      <div class="landing-hero-content">
        <h1 class="landing-hero-title">Vali Admin - A Free Bootstrap 5 Dashboard Template</h1>
        <p class="landing-hero-description">Built with PugJS, SASS, and Bootstrap 5</p>
        <div><a class="btn btn-primary me-4" href="dashboard.html">Demo</a><a class="btn btn-primary" href="https://github.com/pratikborsadiya/vali-admin" target="_blank" rel="noopener">GitHub</a></div><img class="landing-hero-banner" src="images/banner.jpg">
      </div>
    </section>
    <section class="features">
      <h1 class="features-title">Features</h1>
      <div class="features-cards">
        <div class="features-card">
          <div class="feature-icon-backdrop">
            <svg class="feature-icon">
              <use xlink:href="#icon-globe"></use>
            </svg>
          </div>
          <p class="feature-description">Free to use and open-source</p>
        </div>
        <div class="features-card">
          <div class="feature-icon-backdrop">
            <svg class="feature-icon">
              <use xlink:href="#icon-smartphone"></use>
            </svg>
          </div>
          <p class="feature-description">Beautiful and mobile responsive</p>
        </div>
        <div class="features-card">
          <div class="feature-icon-backdrop">
            <svg class="feature-icon">
              <use xlink:href="#icon-zap"></use>
            </svg>
          </div>
          <p class="feature-description">Built with Bootstrap 5, SASS and PUG.js</p>
        </div>
        <div class="features-card">
          <div class="feature-icon-backdrop">
            <svg class="feature-icon">
              <use xlink:href="#icon-chart"></use>
            </svg>
          </div>
          <p class="feature-description">Chart.js integration to display responsive charts</p>
        </div>
        <div class="features-card">
          <div class="feature-icon-backdrop">
            <svg class="feature-icon">
              <use xlink:href="#icon-layout"></use>
            </svg>
          </div>
          <p class="feature-description">Seven pre built pages including login, user profile and print friendly invoice page</p>
        </div>
        <div class="features-card">
          <div class="feature-icon-backdrop">
            <svg class="feature-icon">
              <use xlink:href="#icon-grid"></use>
            </svg>
          </div>
          <p class="feature-description">Data tables with sort, search and paginate functions</p>
        </div>
      </div>
    </section>
    <footer class="landing-footer">Created by <b>Pratik Borsadiya</b> & <a href="https://github.com/pratikborsadiya/vali-admin/graphs/contributors" target="_blank" rel="noopener">contributors</a></footer>
  </body>
</html>