// Componant: Sidebar Mini

@media (min-width: 768px) {
	.sidebar-mini.sidenav-toggled {
		.app-sidebar__user-name,
		.app-sidebar__user-designation,
		.treeview-indicator {
			display: none;
		}
		.app-sidebar__user-avatar {
			width: 30px;
			height: 30px;
		}
		.app-content { margin-left: 50px; }
		.app-sidebar {
			left: 0;
			width: 50px;
			overflow: hidden;
			&:hover { overflow: visible; }
		}
		.app-menu__item {
			overflow: hidden;
			&:hover {
				overflow: visible;
				.app-menu__label { opacity: 1; }
				& + .treeview-menu { visibility: visible; }
			}
		}
		.app-menu__label {
			display: block;
			position: absolute;
			top: 0;
			left: 50px;
			min-width: 180px;
			padding: 16px 5px 16px 20px;
			margin-left: -3px;
			line-height: 1;
			opacity: 0;
			background: darken($sidebar-color, 10);
			border-top-right-radius: 4px;
			border-bottom-right-radius: 4px;
		}
		.treeview {
			&:hover {
				.app-menu__item {
					overflow: visible;
					background: darken($sidebar-color, 10);
					border-left-color: $primary-color;
					@if $sidebar-accent == dark { color: $sidebar-dark-link-color }
					@if $sidebar-accent == light { color: $primary-color }
				}
				.app-menu__label { opacity: 1; }
				.treeview-menu {
					max-height: 100vh;
					opacity: 1;
					visibility: visible;
					z-index: 10;
				}
			}
			.app-menu__label { border-bottom-right-radius: 0; }
		}
		.treeview-menu {
			position: absolute;
			left: 50px;
			min-width: 180px;
			padding: 12px 0;
			opacity: 0;
			border-bottom-right-radius: 4px;
			z-index: 9;
			visibility: hidden;
			transition: visibility 0.3s ease;
		}
	}
}