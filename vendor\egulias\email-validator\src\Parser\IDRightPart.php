<?php

namespace E<PERSON>lias\EmailValidator\Parser;

use <PERSON><PERSON><PERSON>s\EmailValidator\EmailLexer;
use E<PERSON>lias\EmailValidator\Result\Result;
use Egulias\EmailValidator\Result\ValidEmail;
use <PERSON><PERSON><PERSON>s\EmailValidator\Result\InvalidEmail;
use E<PERSON>lias\EmailValidator\Result\Reason\ExpectingATEXT;

class IDRightPart extends DomainPart
{
    protected function validateTokens(bool $hasComments) : Result
    {
        $invalidDomainTokens = [
            EmailLexer::S_DQUOTE => true,
            EmailLexer::S_SQUOTE => true,
            EmailLexer::S_BACKTICK => true,
            EmailLexer::S_SEMICOLON => true,
            EmailLexer::S_GREATERTHAN => true,
            EmailLexer::S_LOWERTHAN => true,
        ];

        if (isset($invalidDomainTokens[((array) $this->lexer->token)['type']])) {
            return new InvalidEmail(new ExpectingATEXT('Invalid token in domain: ' . ((array) $this->lexer->token)['value']), ((array) $this->lexer->token)['value']);
        }
        return new ValidEmail();
    }
}
